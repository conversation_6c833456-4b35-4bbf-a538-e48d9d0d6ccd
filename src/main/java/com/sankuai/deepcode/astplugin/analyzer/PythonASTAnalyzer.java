package com.sankuai.deepcode.astplugin.analyzer;

import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.util.PsiTreeUtil;
import com.sankuai.deepcode.astplugin.model.AnalysisNode;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.CallRelation;
import com.sankuai.deepcode.astplugin.model.Language;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Python AST分析器 - 增强版
 * 支持Python类定义、__init__方法调用、super()调用等的分析
 * 确保节点ID的唯一性，遵循指定的路径格式
 */
public class PythonASTAnalyzer {
    private static final boolean DEBUG_MODE = true;

    // 缓存 Python 插件可用性检测结果
    private Boolean pythonPluginAvailable = null;

    public AnalysisResult analyze(PsiFile pythonFile) {
        debugLog("开始分析Python文件: " + pythonFile.getName());
        
        AnalysisResult result = new AnalysisResult(
                pythonFile.getName(),
                Language.PYTHON
        );

        try {
            // 获取模块名
            String moduleName = extractModuleName(pythonFile);
            debugLog("模块名: " + moduleName);

            // 存储所有发现的函数和类，用于后续调用关系分析
            Map<String, AnalysisNode> functions = new HashMap<>();
            Map<String, AnalysisNode> classes = new HashMap<>();

            // 分析Python文件结构
            analyzePythonStructure(pythonFile, result, moduleName, functions, classes);

            // 分析调用关系
            analyzePythonCallRelations(pythonFile, result, functions);

            debugLog("Python文件分析完成");
            
        } catch (Exception e) {
            result.addError("分析Python文件时发生错误: " + e.getMessage());
            debugLog("分析错误: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 提取模块名
     */
    private String extractModuleName(PsiFile pythonFile) {
        try {
            String fileName = pythonFile.getName();
            if (fileName.endsWith(".py")) {
                return fileName.substring(0, fileName.length() - 3);
            }
            return fileName;
        } catch (Exception e) {
            debugLog("提取模块名失败: " + e.getMessage());
            return "unknown_module";
        }
    }

    /**
     * 分析Python文件结构
     */
    private void analyzePythonStructure(PsiFile pythonFile, AnalysisResult result, String moduleName,
                                      Map<String, AnalysisNode> functions, Map<String, AnalysisNode> classes) {
        try {
            // 遍历所有顶级元素
            PsiElement[] children = pythonFile.getChildren();
            
            for (PsiElement child : children) {
                analyzePythonElement(child, result, moduleName, null, functions, classes);
            }
            
        } catch (Exception e) {
            debugLog("分析Python结构时发生错误: " + e.getMessage());
            result.addError("分析Python结构时发生错误: " + e.getMessage());
        }
    }

    /**
     * 递归分析Python元素
     */
    private void analyzePythonElement(PsiElement element, AnalysisResult result, String moduleName, 
                                    String currentClassName, Map<String, AnalysisNode> functions, 
                                    Map<String, AnalysisNode> classes) {
        try {
            if (isPythonClassDefinition(element)) {
                // 分析类定义
                analyzePythonClass(element, result, moduleName, functions, classes);
                
            } else if (isPythonFunctionDefinition(element)) {
                // 分析函数定义
                analyzePythonFunction(element, result, moduleName, currentClassName, functions);
                
            } else if (isPythonAssignmentStatement(element)) {
                // 分析变量赋值
                analyzePythonVariable(element, result, moduleName, currentClassName);
            }

            // 递归分析子元素
            for (PsiElement child : element.getChildren()) {
                analyzePythonElement(child, result, moduleName, currentClassName, functions, classes);
            }
            
        } catch (Exception e) {
            debugLog("分析Python元素时发生错误: " + e.getMessage());
        }
    }

    /**
     * 分析Python类定义
     */
    private void analyzePythonClass(PsiElement classElement, AnalysisResult result, String moduleName,
                                  Map<String, AnalysisNode> functions, Map<String, AnalysisNode> classes) {
        try {
            String className = extractPythonClassName(classElement);
            if (className == null) return;

            int lineNumber = getElementLineNumber(classElement);
            
            // 生成类ID和签名 - 格式: module.class_name
            String classId = generateClassId(className, moduleName);
            String classSignature = generateClassSignature(className, moduleName);

            AnalysisNode classNode = new AnalysisNode(
                classId,
                AnalysisNode.NodeType.CLASS,
                className,
                "",  // 类本身没有包含类
                moduleName != null ? moduleName : "",
                lineNumber,
                classSignature,
                moduleName,
                result.getFileName(),
                "Python"
            );

            result.addNode(classNode);
            classes.put(classId, classNode);
            result.incrementStatistics("classes");
            
            debugLog("发现Python类: " + classId + " 在第 " + lineNumber + " 行");

            // 分析类中的方法
            analyzePythonClassMethods(classElement, className, result, moduleName, result.getFileName(), functions);

        } catch (Exception e) {
            debugLog("分析Python类时发生错误: " + e.getMessage());
        }
    }

    /**
     * 分析Python类中的方法
     */
    private void analyzePythonClassMethods(PsiElement classElement, String className, AnalysisResult result, 
                                         String moduleName, String filePath, Map<String, AnalysisNode> functions) {
        try {
            Collection<PsiElement> methodElements = PsiTreeUtil.findChildrenOfType(classElement, PsiElement.class);

            for (PsiElement element : methodElements) {
                if (isPythonFunctionDefinition(element)) {
                    String methodName = extractPythonFunctionName(element);
                    String parameters = extractPythonFunctionParameters(element);

                    if (methodName != null) {
                        int lineNumber = getElementLineNumber(element);

                        // 生成方法ID - 格式: module.class_name.method_name
                        String methodId = generateFunctionId(methodName, className, moduleName, parameters);
                        String methodSignature = generateFunctionSignature(methodName, className, moduleName, parameters);
                        
                        AnalysisNode methodNode = new AnalysisNode(
                            methodId,
                            AnalysisNode.NodeType.FUNCTION,
                            methodName,
                            className,
                            moduleName != null ? moduleName : "",
                            lineNumber,
                            methodSignature,
                            moduleName,
                            filePath,
                            "Python"
                        );

                        result.addNode(methodNode);
                        
                        // 使用完整的方法ID作为key，确保唯一性
                        functions.put(methodId, methodNode);
                        
                        result.incrementStatistics("functions");
                        debugLog("发现Python方法: " + methodId + " 在第 " + lineNumber + " 行");
                        
                        // 特殊处理__init__方法
                        if ("__init__".equals(methodName)) {
                            analyzeInitMethodCalls(element, methodNode, result, functions, className, moduleName);
                        }
                    }
                }
            }
        } catch (Exception e) {
            debugLog("分析Python类方法时发生错误: " + e.getMessage());
        }
    }

    /**
     * 分析Python函数定义
     */
    private void analyzePythonFunction(PsiElement functionElement, AnalysisResult result, String moduleName,
                                     String currentClassName, Map<String, AnalysisNode> functions) {
        try {
            String functionName = extractPythonFunctionName(functionElement);
            String parameters = extractPythonFunctionParameters(functionElement);
            
            if (functionName == null) return;

            int lineNumber = getElementLineNumber(functionElement);
            
            // 生成函数ID - 格式: module.function_name (模块级函数)
            String functionId = generateFunctionId(functionName, currentClassName, moduleName, parameters);
            String functionSignature = generateFunctionSignature(functionName, currentClassName, moduleName, parameters);

            AnalysisNode functionNode = new AnalysisNode(
                functionId,
                AnalysisNode.NodeType.FUNCTION,
                functionName,
                currentClassName != null ? currentClassName : "",
                moduleName != null ? moduleName : "",
                lineNumber,
                functionSignature,
                moduleName,
                result.getFileName(),
                "Python"
            );

            result.addNode(functionNode);
            functions.put(functionId, functionNode);
            result.incrementStatistics("functions");
            
            debugLog("发现Python函数: " + functionId + " 在第 " + lineNumber + " 行");

        } catch (Exception e) {
            debugLog("分析Python函数时发生错误: " + e.getMessage());
        }
    }

    /**
     * 分析Python变量赋值
     */
    private void analyzePythonVariable(PsiElement assignmentElement, AnalysisResult result, String moduleName,
                                     String currentClassName) {
        try {
            String variableName = extractPythonVariableName(assignmentElement);
            if (variableName == null) return;

            int lineNumber = getElementLineNumber(assignmentElement);
            
            // 生成变量ID - 格式: module.variable_name
            String variableId = generateVariableId(variableName, moduleName);
            String variableSignature = generateVariableSignature(variableName, moduleName);

            AnalysisNode variableNode = new AnalysisNode(
                variableId,
                AnalysisNode.NodeType.VARIABLE,
                variableName,
                currentClassName != null ? currentClassName : "",
                moduleName != null ? moduleName : "",
                lineNumber,
                variableSignature,
                moduleName,
                result.getFileName(),
                "Python"
            );

            result.addNode(variableNode);
            result.incrementStatistics("variables");
            
            debugLog("发现Python变量: " + variableId + " 在第 " + lineNumber + " 行");

        } catch (Exception e) {
            debugLog("分析Python变量时发生错误: " + e.getMessage());
        }
    }

    /**
     * 分析__init__方法中的特殊调用
     */
    private void analyzeInitMethodCalls(PsiElement initMethodElement, AnalysisNode initMethodNode, 
                                      AnalysisResult result, Map<String, AnalysisNode> functions, 
                                      String className, String moduleName) {
        try {
            debugLog("分析__init__方法调用，类: " + className);
            
            // 分析super().__init__()调用
            analyzeSuperInitCalls(initMethodElement, initMethodNode, result, className, moduleName);
            
            // 分析直接的父类__init__调用
            analyzeParentInitCalls(initMethodElement, initMethodNode, result, functions, className, moduleName);
            
            // 分析组合模式中的其他类__init__调用
            analyzeCompositionInitCalls(initMethodElement, initMethodNode, result, functions, className, moduleName);
            
        } catch (Exception e) {
            debugLog("分析__init__方法调用时发生错误: " + e.getMessage());
        }
    }

    /**
     * 分析super().__init__()调用
     */
    private void analyzeSuperInitCalls(PsiElement initMethodElement, AnalysisNode initMethodNode, 
                                     AnalysisResult result, String className, String moduleName) {
        try {
            String methodText = initMethodElement.getText();
            if (methodText == null) return;

            // 匹配super().__init__()调用的各种形式
            Pattern[] superPatterns = {
                Pattern.compile("super\\(\\)\\.__init__\\s*\\([^)]*\\)"),
                Pattern.compile("super\\([^)]*\\)\\.__init__\\s*\\([^)]*\\)"),
                Pattern.compile("super\\(\\s*" + className + "\\s*,\\s*self\\s*\\)\\.__init__\\s*\\([^)]*\\)")
            };

            for (Pattern pattern : superPatterns) {
                Matcher matcher = pattern.matcher(methodText);
                while (matcher.find()) {
                    String superCall = matcher.group();
                    int lineNumber = getLineNumberFromText(methodText, matcher.start());
                    
                    // 创建super().__init__节点，使用外部节点ID格式
                    String superInitId = generateExternalNodeId("__init__", "super." + className, "function");
                    AnalysisNode superInitNode = new AnalysisNode(
                        superInitId,
                        AnalysisNode.NodeType.FUNCTION,
                        "__init__",
                        "super(" + className + ")",
                        moduleName != null ? moduleName : "",
                        lineNumber,
                        "super().__init__()",
                        moduleName,
                        "builtin",
                        "Python"
                    );

                    result.addNode(superInitNode);
                    
                    // 创建调用关系
                    CallRelation superCallRelation = new CallRelation(
                        initMethodNode, 
                        superInitNode,
                        lineNumber,
                        superCall,
                        true, // 外部调用
                        Arrays.asList(new CallRelation.CallInstance(lineNumber, superCall))
                    );
                    
                    result.addCallRelation(superCallRelation);
                    result.incrementStatistics("function_calls");
                    
                    debugLog("发现super().__init__调用: " + className + ".__init__ -> " + superInitId + " 在第 " + lineNumber + " 行");
                }
            }
        } catch (Exception e) {
            debugLog("分析super().__init__调用时发生错误: " + e.getMessage());
        }
    }

    /**
     * 分析直接的父类__init__调用
     */
    private void analyzeParentInitCalls(PsiElement initMethodElement, AnalysisNode initMethodNode, 
                                      AnalysisResult result, Map<String, AnalysisNode> functions, 
                                      String className, String moduleName) {
        try {
            String methodText = initMethodElement.getText();
            if (methodText == null) return;

            // 匹配ParentClass.__init__(self, ...)调用
            Pattern parentInitPattern = Pattern.compile("([A-Z][a-zA-Z0-9_]*)\\.__init__\\s*\\(\\s*self\\s*[,)]");
            Matcher matcher = parentInitPattern.matcher(methodText);

            while (matcher.find()) {
                String parentClassName = matcher.group(1);
                
                // 跳过自身类名
                if (parentClassName.equals(className)) {
                    continue;
                }
                
                String parentInitCall = matcher.group();
                int lineNumber = getLineNumberFromText(methodText, matcher.start());
                
                // 查找或创建父类__init__节点
                String parentInitId = generateFunctionId("__init__", parentClassName, moduleName, "");
                AnalysisNode parentInitNode = functions.get(parentInitId);
                
                if (parentInitNode == null) {
                    // 创建外部父类__init__节点
                    parentInitId = generateExternalNodeId("__init__", parentClassName, "function");
                    parentInitNode = new AnalysisNode(
                        parentInitId,
                        AnalysisNode.NodeType.FUNCTION,
                        "__init__",
                        parentClassName,
                        moduleName != null ? moduleName : "",
                        0, // 外部方法没有行号
                        parentClassName + ".__init__()",
                        moduleName,
                        "external",
                        "Python"
                    );
                    result.addNode(parentInitNode);
                }
                
                // 创建调用关系
                CallRelation parentCallRelation = new CallRelation(
                    initMethodNode, 
                    parentInitNode,
                    lineNumber,
                    parentInitCall,
                    true, // 外部调用
                    Arrays.asList(new CallRelation.CallInstance(lineNumber, parentInitCall))
                );
                
                result.addCallRelation(parentCallRelation);
                result.incrementStatistics("function_calls");
                
                debugLog("发现父类__init__调用: " + className + ".__init__ -> " + parentClassName + ".__init__ 在第 " + lineNumber + " 行");
            }
        } catch (Exception e) {
            debugLog("分析父类__init__调用时发生错误: " + e.getMessage());
        }
    }

    /**
     * 分析组合模式中的其他类__init__调用
     */
    private void analyzeCompositionInitCalls(PsiElement initMethodElement, AnalysisNode initMethodNode, 
                                           AnalysisResult result, Map<String, AnalysisNode> functions, 
                                           String className, String moduleName) {
        try {
            String methodText = initMethodElement.getText();
            if (methodText == null) return;

            // 匹配self.attribute = SomeClass(...)模式
            Pattern compositionPattern = Pattern.compile("self\\.[a-zA-Z_][a-zA-Z0-9_]*\\s*=\\s*([A-Z][a-zA-Z0-9_]*)\\s*\\(");
            Matcher matcher = compositionPattern.matcher(methodText);

            while (matcher.find()) {
                String composedClassName = matcher.group(1);
                
                // 跳过内置类型
                if (isPythonBuiltinType(composedClassName.toLowerCase())) {
                    continue;
                }
                
                String compositionCall = matcher.group();
                int lineNumber = getLineNumberFromText(methodText, matcher.start());
                
                // 查找或创建组合类的__init__节点
                String composedInitId = generateFunctionId("__init__", composedClassName, moduleName, "");
                AnalysisNode composedInitNode = functions.get(composedInitId);
                
                if (composedInitNode == null) {
                    // 创建外部组合类__init__节点
                    composedInitId = generateExternalNodeId("__init__", composedClassName, "function");
                    composedInitNode = new AnalysisNode(
                        composedInitId,
                        AnalysisNode.NodeType.FUNCTION,
                        "__init__",
                        composedClassName,
                        moduleName != null ? moduleName : "",
                        0, // 外部方法没有行号
                        composedClassName + ".__init__()",
                        moduleName,
                        "external",
                        "Python"
                    );
                    result.addNode(composedInitNode);
                }
                
                // 创建调用关系
                CallRelation compositionCallRelation = new CallRelation(
                    initMethodNode, 
                    composedInitNode,
                    lineNumber,
                    compositionCall,
                    true, // 外部调用
                    Arrays.asList(new CallRelation.CallInstance(lineNumber, compositionCall))
                );
                
                result.addCallRelation(compositionCallRelation);
                result.incrementStatistics("function_calls");
                
                debugLog("发现组合__init__调用: " + className + ".__init__ -> " + composedClassName + ".__init__ 在第 " + lineNumber + " 行");
            }
        } catch (Exception e) {
            debugLog("分析组合__init__调用时发生错误: " + e.getMessage());
        }
    }

    /**
     * 分析Python函数调用关系
     */
    private void analyzePythonCallRelations(PsiFile pythonFile, AnalysisResult result, Map<String, AnalysisNode> functions) {
        try {
            debugLog("开始分析Python调用关系");
            
            Map<String, List<CallRelation.CallInstance>> aggregatedCalls = new HashMap<>();

            // 遍历所有函数，分析其中的调用关系
            for (AnalysisNode function : functions.values()) {
                analyzeFunctionCalls(pythonFile, function, aggregatedCalls, functions);
            }

            // 创建调用关系对象
            createCallRelations(result, aggregatedCalls, functions);
            
            debugLog("Python调用关系分析完成");
            
        } catch (Exception e) {
            debugLog("分析Python调用关系时发生错误: " + e.getMessage());
            result.addError("分析Python调用关系时发生错误: " + e.getMessage());
        }
    }

    /**
     * 分析单个函数中的调用关系
     */
    private void analyzeFunctionCalls(PsiFile pythonFile, AnalysisNode callerFunction,
                                    Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                                    Map<String, AnalysisNode> functions) {
        try {
            // 查找函数对应的PSI元素
            Collection<PsiElement> functionElements = PsiTreeUtil.findChildrenOfType(pythonFile, PsiElement.class);
            
            for (PsiElement element : functionElements) {
                if (isPythonFunctionDefinition(element)) {
                    String functionName = extractPythonFunctionName(element);
                    if (functionName != null && callerFunction.getName().equals(functionName)) {
                        // 分析函数体中的调用
                        analyzeFunctionBody(element, callerFunction, aggregatedCalls, functions);
                        break;
                    }
                }
            }
            
        } catch (Exception e) {
            debugLog("分析函数调用时发生错误: " + e.getMessage());
        }
    }

    /**
     * 分析函数体中的调用
     */
    private void analyzeFunctionBody(PsiElement functionElement, AnalysisNode callerFunction,
                                   Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                                   Map<String, AnalysisNode> functions) {
        try {
            String functionText = functionElement.getText();
            if (functionText == null) return;

            // 分析各种调用模式
            analyzeFunctionCallPatterns(functionText, callerFunction, aggregatedCalls, functionElement, functions);
            
        } catch (Exception e) {
            debugLog("分析函数体时发生错误: " + e.getMessage());
        }
    }

    /**
     * 分析函数调用模式
     */
    private void analyzeFunctionCallPatterns(String functionText, AnalysisNode callerFunction,
                                           Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                                           PsiElement element, Map<String, AnalysisNode> functions) {
        try {
            // 分析普通函数调用
            analyzeRegularFunctionCalls(functionText, callerFunction, aggregatedCalls, element, functions);
            
            // 分析super()方法调用
            analyzeSuperMethodCalls(functionText, callerFunction, aggregatedCalls, element);
            
            // 分析方法调用
            analyzeMethodCalls(functionText, callerFunction, aggregatedCalls, element, functions);
            
        } catch (Exception e) {
            debugLog("分析函数调用模式时发生错误: " + e.getMessage());
        }
    }

    /**
     * 分析普通函数调用
     */
    private void analyzeRegularFunctionCalls(String functionText, AnalysisNode callerFunction,
                                           Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                                           PsiElement element, Map<String, AnalysisNode> functions) {
        try {
            // 匹配函数调用模式: function_name(args)
            Pattern functionCallPattern = Pattern.compile("([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(");
            Matcher matcher = functionCallPattern.matcher(functionText);

            while (matcher.find()) {
                String calledFunctionName = matcher.group(1);
                
                // 跳过关键字和内置函数
                if (isPythonKeyword(calledFunctionName) || isPythonBuiltinFunction(calledFunctionName)) {
                    continue;
                }

                // 查找被调用的函数
                AnalysisNode calledFunction = findFunctionByName(calledFunctionName, functions);
                
                if (calledFunction != null) {
                    addCallRelation(callerFunction, calledFunction, aggregatedCalls, element, 
                                  matcher.group(), false);
                    debugLog("发现函数调用: " + callerFunction.getId() + " -> " + calledFunction.getId());
                }
            }
            
        } catch (Exception e) {
            debugLog("分析普通函数调用时发生错误: " + e.getMessage());
        }
    }

    /**
     * 分析super()方法调用
     */
    private void analyzeSuperMethodCalls(String functionText, AnalysisNode callerFunction,
                                       Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                                       PsiElement element) {
        try {
            // 分析各种super()调用模式
            analyzeSuperCallPatterns(functionText, callerFunction, aggregatedCalls, element);
            
        } catch (Exception e) {
            debugLog("分析super()方法调用时发生错误: " + e.getMessage());
        }
    }

    /**
     * 分析各种super()调用模式
     */
    private void analyzeSuperCallPatterns(String functionText, AnalysisNode callerFunction,
                                        Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                                        PsiElement element) {
        // 模式1: super().method_name()
        Pattern superPattern1 = Pattern.compile("super\\(\\)\\.([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(");
        analyzeSuperPattern(functionText, callerFunction, aggregatedCalls, element, superPattern1, "super()");

        // 模式2: super(ClassName, self).method_name()
        Pattern superPattern2 = Pattern.compile("super\\(\\s*([A-Z][a-zA-Z0-9_]*)\\s*,\\s*self\\s*\\)\\.([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(");
        Matcher matcher2 = superPattern2.matcher(functionText);
        while (matcher2.find()) {
            String className = matcher2.group(1);
            String methodName = matcher2.group(2);
            String superMethodId = generateExternalNodeId(methodName, "super." + className, "function");
            AnalysisNode superMethod = createSuperMethodNode(methodName, superMethodId, className);
            addCallRelation(callerFunction, superMethod, aggregatedCalls, element, matcher2.group(), true);
            debugLog("发现super(" + className + ")方法调用: " + callerFunction.getName() + " -> " + superMethodId);
        }

        // 模式3: 直接的父类方法调用 ParentClass.method_name(self, ...)
        Pattern parentMethodPattern = Pattern.compile("([A-Z][a-zA-Z0-9_]*)\\.([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(\\s*self\\s*[,)]");
        Matcher parentMatcher = parentMethodPattern.matcher(functionText);
        while (parentMatcher.find()) {
            String parentClassName = parentMatcher.group(1);
            String methodName = parentMatcher.group(2);
            
            // 跳过当前类的方法调用
            String currentClassName = extractClassNameFromFunction(callerFunction);
            if (!parentClassName.equals(currentClassName)) {
                String parentMethodId = generateExternalNodeId(methodName, parentClassName, "function");
                AnalysisNode parentMethod = createParentMethodNode(methodName, parentMethodId, parentClassName);
                addCallRelation(callerFunction, parentMethod, aggregatedCalls, element, parentMatcher.group(), true);
                debugLog("发现父类方法调用: " + callerFunction.getName() + " -> " + parentMethodId);
            }
        }
    }

    /**
     * 分析特定的super()调用模式
     */
    private void analyzeSuperPattern(String functionText, AnalysisNode callerFunction,
                                   Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                                   PsiElement element, Pattern pattern, String superType) {
        Matcher matcher = pattern.matcher(functionText);
        while (matcher.find()) {
            String methodName = matcher.group(matcher.groupCount()); // 获取最后一个捕获组（方法名）
            String superMethodId = generateExternalNodeId(methodName, superType, "function");
            AnalysisNode superMethod = createSuperMethodNode(methodName, superMethodId, superType);
            addCallRelation(callerFunction, superMethod, aggregatedCalls, element, matcher.group(), true);
            debugLog("发现" + superType + "方法调用: " + callerFunction.getName() + " -> " + superMethodId);
        }
    }

    /**
     * 分析方法调用
     */
    private void analyzeMethodCalls(String functionText, AnalysisNode callerFunction,
                                  Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                                  PsiElement element, Map<String, AnalysisNode> functions) {
        try {
            // 匹配方法调用模式: object.method_name(args)
            Pattern methodCallPattern = Pattern.compile("([a-zA-Z_][a-zA-Z0-9_]*)\\.([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(");
            Matcher matcher = methodCallPattern.matcher(functionText);

            while (matcher.find()) {
                String objectName = matcher.group(1);
                String methodName = matcher.group(2);
                
                // 跳过self调用（已在其他地方处理）
                if ("self".equals(objectName)) {
                    continue;
                }

                // 查找对应的方法
                String methodId = generateFunctionId(methodName, objectName, null, "");
                AnalysisNode calledMethod = functions.get(methodId);
                
                if (calledMethod == null) {
                    // 创建外部方法节点
                    methodId = generateExternalNodeId(methodName, objectName, "function");
                    calledMethod = createExternalMethodNode(methodName, methodId, objectName);
                }
                
                addCallRelation(callerFunction, calledMethod, aggregatedCalls, element, matcher.group(), true);
                debugLog("发现方法调用: " + callerFunction.getId() + " -> " + methodId);
            }
            
        } catch (Exception e) {
            debugLog("分析方法调用时发生错误: " + e.getMessage());
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 检查是否为Python类定义
     */
    private boolean isPythonClassDefinition(PsiElement element) {
        String text = element.getText();
        return text != null && text.trim().startsWith("class ");
    }

    /**
     * 检查是否为Python函数定义
     */
    private boolean isPythonFunctionDefinition(PsiElement element) {
        String text = element.getText();
        return text != null && text.trim().startsWith("def ");
    }

    /**
     * 检查是否为Python赋值语句
     */
    private boolean isPythonAssignmentStatement(PsiElement element) {
        String text = element.getText();
        return text != null && text.contains("=") && !text.contains("==") && !text.contains("!=");
    }

    /**
     * 提取Python类名
     */
    private String extractPythonClassName(PsiElement element) {
        try {
            String text = element.getText();
            if (text == null) return null;
            
            String[] lines = text.split("\n");
            for (String line : lines) {
                if (line.trim().startsWith("class ")) {
                    return extractClassName(line);
                }
            }
        } catch (Exception e) {
            debugLog("提取Python类名时发生错误: " + e.getMessage());
        }
        return null;
    }

    /**
     * 提取Python函数名
     */
    private String extractPythonFunctionName(PsiElement element) {
        try {
            String text = element.getText();
            if (text == null) return null;
            
            String[] lines = text.split("\n");
            for (String line : lines) {
                if (line.trim().startsWith("def ")) {
                    return extractFunctionName(line);
                }
            }
        } catch (Exception e) {
            debugLog("提取Python函数名时发生错误: " + e.getMessage());
        }
        return null;
    }

    /**
     * 提取Python函数参数
     */
    private String extractPythonFunctionParameters(PsiElement element) {
        try {
            String text = element.getText();
            if (text == null) return "";
            
            String[] lines = text.split("\n");
            for (String line : lines) {
                if (line.trim().startsWith("def ")) {
                    return extractFunctionParameters(line);
                }
            }
        } catch (Exception e) {
            debugLog("提取Python函数参数时发生错误: " + e.getMessage());
        }
        return "";
    }

    /**
     * 提取Python变量名
     */
    private String extractPythonVariableName(PsiElement element) {
        try {
            String text = element.getText();
            if (text == null) return null;
            
            String[] lines = text.split("\n");
            for (String line : lines) {
                if (line.contains("=") && !line.contains("==") && !line.contains("!=")) {
                    return extractVariableName(line);
                }
            }
        } catch (Exception e) {
            debugLog("提取Python变量名时发生错误: " + e.getMessage());
        }
        return null;
    }

    /**
     * 从文本中获取指定位置的行号
     */
    private int getLineNumberFromText(String text, int position) {
        try {
            String beforePosition = text.substring(0, Math.min(position, text.length()));
            return beforePosition.split("\n").length;
        } catch (Exception e) {
            return 1;
        }
    }

    /**
     * 从函数节点中提取类名
     */
    private String extractClassNameFromFunction(AnalysisNode functionNode) {
        try {
            String className = functionNode.getClassName();
            if (className != null && !className.isEmpty()) {
                return className;
            }
            
            // 从函数ID中提取类名
            String functionId = functionNode.getId();
            if (functionId.contains(".")) {
                String[] parts = functionId.split("\\.");
                if (parts.length >= 3) {
                    return parts[parts.length - 2]; // 倒数第二个部分是类名
                }
            }
        } catch (Exception e) {
            debugLog("从函数节点提取类名时发生错误: " + e.getMessage());
        }
        return "";
    }

    /**
     * 创建super()方法节点
     */
    private AnalysisNode createSuperMethodNode(String methodName, String methodId, String superType) {
        return new AnalysisNode(
            methodId,
            AnalysisNode.NodeType.FUNCTION,
            methodName,
            superType,
            superType,
            0, // super方法没有行号
            superType + "." + methodName + "()",
            superType,
            "builtin",
            "Python"
        );
    }

    /**
     * 创建父类方法节点
     */
    private AnalysisNode createParentMethodNode(String methodName, String methodId, String parentClassName) {
        return new AnalysisNode(
            methodId,
            AnalysisNode.NodeType.FUNCTION,
            methodName,
            parentClassName,
            parentClassName,
            0, // 父类方法没有行号
            parentClassName + "." + methodName + "()",
            parentClassName,
            "external",
            "Python"
        );
    }

    /**
     * 创建外部方法节点
     */
    private AnalysisNode createExternalMethodNode(String methodName, String methodId, String objectName) {
        return new AnalysisNode(
            methodId,
            AnalysisNode.NodeType.FUNCTION,
            methodName,
            objectName,
            objectName,
            0, // 外部方法没有行号
            objectName + "." + methodName + "()",
            objectName,
            "external",
            "Python"
        );
    }

    /**
     * 添加调用关系
     */
    private void addCallRelation(AnalysisNode caller, AnalysisNode callee,
                               Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                               PsiElement element, String elementText, boolean isExternal) {
        String aggregationKey = caller.getId() + " -> " + callee.getId();

        List<CallRelation.CallInstance> instances = aggregatedCalls.computeIfAbsent(
            aggregationKey, k -> new ArrayList<>());

        instances.add(new CallRelation.CallInstance(
            getElementLineNumber(element),
            elementText.trim() + (isExternal ? " // external call" : "")
        ));
    }

    /**
     * 创建调用关系对象
     */
    private void createCallRelations(AnalysisResult result, 
                                   Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                                   Map<String, AnalysisNode> functions) {
        try {
            for (Map.Entry<String, List<CallRelation.CallInstance>> entry : aggregatedCalls.entrySet()) {
                String[] parts = entry.getKey().split(" -> ");
                if (parts.length != 2) continue;

                String callerId = parts[0];
                String calleeId = parts[1];

                AnalysisNode caller = findNodeById(result, callerId);
                AnalysisNode callee = findNodeById(result, calleeId);

                if (caller != null && callee != null) {
                    List<CallRelation.CallInstance> instances = entry.getValue();
                    CallRelation.CallInstance firstInstance = instances.get(0);

                    CallRelation relation = new CallRelation(
                        caller,
                        callee,
                        firstInstance.getLineNumber(),
                        firstInstance.getExpression(),
                        calleeId.startsWith("external.") || calleeId.startsWith("builtin."),
                        instances
                    );

                    result.addCallRelation(relation);
                    result.incrementStatistics("function_calls");
                }
            }
        } catch (Exception e) {
            debugLog("创建调用关系时发生错误: " + e.getMessage());
        }
    }

    /**
     * 根据名称查找函数
     */
    private AnalysisNode findFunctionByName(String functionName, Map<String, AnalysisNode> functions) {
        for (AnalysisNode function : functions.values()) {
            if (function.getName().equals(functionName)) {
                return function;
            }
        }
        return null;
    }

    /**
     * 根据ID查找节点
     */
    private AnalysisNode findNodeById(AnalysisResult result, String nodeId) {
        return result.getNodes().get(nodeId);
    }

    /**
     * 检查是否为Python关键字
     */
    private boolean isPythonKeyword(String word) {
        String[] keywords = {
            "and", "as", "assert", "break", "class", "continue", "def", "del", "elif", "else",
            "except", "exec", "finally", "for", "from", "global", "if", "import", "in", "is",
            "lambda", "not", "or", "pass", "print", "raise", "return", "try", "while", "with", "yield"
        };
        
        for (String keyword : keywords) {
            if (keyword.equals(word)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否为Python内置函数
     */
    private boolean isPythonBuiltinFunction(String functionName) {
        String[] commonBuiltins = {
            "print", "len", "str", "int", "float", "bool", "list", "dict", "set", "tuple",
            "range", "enumerate", "zip", "map", "filter", "sorted", "reversed", "sum", "max", "min",
            "open", "input", "type", "isinstance", "hasattr", "getattr", "setattr", "delattr",
            "super", "property", "staticmethod", "classmethod", "abs", "round", "pow", "divmod"
        };

        for (String builtin : commonBuiltins) {
            if (builtin.equals(functionName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否为Python内置类型
     */
    private boolean isPythonBuiltinType(String typeName) {
        String[] builtinTypes = {
            "str", "int", "float", "bool", "list", "dict", "set", "tuple",
            "bytes", "bytearray", "frozenset", "complex", "range", "enumerate",
            "zip", "map", "filter", "reversed", "sorted"
        };

        for (String builtin : builtinTypes) {
            if (builtin.equals(typeName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 从类定义行提取类名
     */
    private String extractClassName(String line) {
        try {
            String trimmed = line.trim();
            if (trimmed.startsWith("class ")) {
                String afterClass = trimmed.substring(6).trim();
                int colonIndex = afterClass.indexOf(':');
                int parenIndex = afterClass.indexOf('(');

                int endIndex = afterClass.length();
                if (colonIndex > 0) endIndex = Math.min(endIndex, colonIndex);
                if (parenIndex > 0) endIndex = Math.min(endIndex, parenIndex);

                return afterClass.substring(0, endIndex).trim();
            }
        } catch (Exception e) {
            debugLog("从行中提取类名时发生错误: " + line);
        }
        return null;
    }

    /**
     * 从函数定义行提取函数名
     */
    private String extractFunctionName(String line) {
        try {
            String trimmed = line.trim();
            if (trimmed.startsWith("def ")) {
                String afterDef = trimmed.substring(4).trim();
                int parenIndex = afterDef.indexOf('(');
                if (parenIndex > 0) {
                    return afterDef.substring(0, parenIndex).trim();
                }
            }
        } catch (Exception e) {
            debugLog("从行中提取函数名时发生错误: " + line);
        }
        return null;
    }

    /**
     * 从函数定义行提取参数列表
     */
    private String extractFunctionParameters(String line) {
        try {
            String trimmed = line.trim();
            if (trimmed.startsWith("def ")) {
                int startParen = trimmed.indexOf('(');
                int endParen = trimmed.indexOf(')', startParen);
                if (startParen > 0 && endParen > startParen) {
                    return trimmed.substring(startParen + 1, endParen).trim();
                }
            }
        } catch (Exception e) {
            debugLog("从行中提取函数参数时发生错误: " + line);
        }
        return "";
    }

    /**
     * 从赋值语句提取变量名
     */
    private String extractVariableName(String line) {
        try {
            String trimmed = line.trim();
            int equalIndex = trimmed.indexOf('=');
            if (equalIndex > 0) {
                String varPart = trimmed.substring(0, equalIndex).trim();
                String[] parts = varPart.split("\\s+");
                if (parts.length > 0) {
                    return parts[0];
                }
            }
        } catch (Exception e) {
            debugLog("从行中提取变量名时发生错误: " + line);
        }
        return null;
    }

    /**
     * 获取PSI元素的行号
     */
    private int getElementLineNumber(PsiElement element) {
        try {
            PsiFile containingFile = element.getContainingFile();
            if (containingFile != null) {
                com.intellij.openapi.editor.Document document =
                    com.intellij.psi.PsiDocumentManager.getInstance(element.getProject())
                        .getDocument(containingFile);

                if (document != null) {
                    int offset = element.getTextOffset();
                    return document.getLineNumber(offset) + 1; // 转换为1基索引
                }
            }
        } catch (Exception e) {
            debugLog("获取元素行号时发生错误: " + e.getMessage());
        }
        return 1; // 默认行号
    }

    // ==================== ID和签名生成方法 ====================

    /**
     * 生成模块ID - 使用完整的模块路径
     * 格式: module.submodule.name
     */
    private String generateModuleId(String moduleName) {
        return moduleName != null ? moduleName : "unknown_module";
    }

    /**
     * 生成类ID - 格式: module.class_name
     */
    private String generateClassId(String className, String moduleName) {
        if (moduleName == null || moduleName.isEmpty()) {
            return "unknown_module." + className;
        }
        return moduleName + "." + className;
    }

    /**
     * 生成函数ID - 根据是否在类中决定格式
     * 模块级函数: module.function_name
     * 类中方法: module.class_name.method_name
     */
    private String generateFunctionId(String functionName, String className, String moduleName, String parameters) {
        StringBuilder id = new StringBuilder();
        
        // 添加模块名
        if (moduleName != null && !moduleName.isEmpty()) {
            id.append(moduleName);
        } else {
            id.append("unknown_module");
        }
        
        // 添加类名（如果是类方法）
        if (className != null && !className.isEmpty()) {
            id.append(".").append(className);
        }
        
        // 添加函数名
        id.append(".").append(functionName);
        
        return id.toString();
    }

    /**
     * 生成变量ID - 格式: module.variable_name
     */
    private String generateVariableId(String variableName, String moduleName) {
        if (moduleName == null || moduleName.isEmpty()) {
            return "unknown_module." + variableName;
        }
        return moduleName + "." + variableName;
    }

    /**
     * 生成类签名 - 用于显示
     */
    private String generateClassSignature(String className, String moduleName) {
        if (moduleName == null || moduleName.isEmpty()) {
            return "class " + className;
        }
        return "class " + moduleName + "." + className;
    }

    /**
     * 生成函数签名 - 用于显示，包含参数信息
     */
    private String generateFunctionSignature(String functionName, String className, String moduleName, String parameters) {
        StringBuilder signature = new StringBuilder();
        
        // 添加模块名
        if (moduleName != null && !moduleName.isEmpty()) {
            signature.append(moduleName);
        } else {
            signature.append("unknown_module");
        }
        
        // 添加类名（如果是类方法）
        if (className != null && !className.isEmpty()) {
            signature.append(".").append(className);
        }
        
        // 添加函数名和参数
        signature.append(".").append(functionName).append("(").append(parameters).append(")");
        
        return signature.toString();
    }

    /**
     * 生成变量签名 - 用于显示
     */
    private String generateVariableSignature(String variableName, String moduleName) {
        if (moduleName == null || moduleName.isEmpty()) {
            return variableName;
        }
        return moduleName + "." + variableName;
    }

    /**
     * 生成外部节点ID - 用于super()调用、父类调用等
     */
    private String generateExternalNodeId(String nodeName, String context, String type) {
        return "external." + type + "." + context + "." + nodeName;
    }

    /**
     * 生成内置节点ID - 用于内置函数和方法
     */
    private String generateBuiltinNodeId(String nodeName, String type) {
        return "builtin." + type + "." + nodeName;
    }

    private void debugLog(String message) {
        if (DEBUG_MODE) {
            System.out.println("[PythonASTAnalyzer] " + message);
        }
    }
}