package com.sankuai.deepcode.astplugin.model;

import java.util.*;

/**
 * 分析结果，包含所有分析得到的结构化数据
 */
public class AnalysisResult {
    private final String fileName;
    private final Language language;
    private final Date timestamp;
    
    private final Map<String, AnalysisNode> nodes = new HashMap<>();
    private final List<CallRelation> callRelations = new ArrayList<>();
    private final Map<String, Integer> statistics = new HashMap<>();
    private final List<String> errors = new ArrayList<>();

    public AnalysisResult(String fileName, Language language) {
        this.fileName = fileName;
        this.language = language;
        this.timestamp = new Date();
    }

    // 便捷构造函数，支持字符串参数
    public AnalysisResult(String fileName, String languageCode) {
        this.fileName = fileName;
        this.language = Language.fromCode(languageCode);
        this.timestamp = new Date();
    }

    // 便捷构造函数，根据文件名自动推断语言
    public AnalysisResult(String fileName) {
        this.fileName = fileName;
        this.language = Language.fromFileName(fileName);
        this.timestamp = new Date();
    }

    // 添加节点
    public void addNode(AnalysisNode node) {
        nodes.put(node.getId(), node);
    }

    // 添加调用关系
    public void addCallRelation(CallRelation relation) {
        callRelations.add(relation);
    }

    // 更新统计信息
    public void updateStatistics(String key, int value) {
        statistics.put(key, value);
    }

    public void incrementStatistics(String key) {
        statistics.put(key, statistics.getOrDefault(key, 0) + 1);
    }

    // 添加错误信息
    public void addError(String error) {
        errors.add(error);
    }



    // Getters
    public String getFileName() { return fileName; }
    public Language getLanguage() { return language; }
    public String getLanguageCode() { return language.getCode(); }
    public String getLanguageDisplayName() { return language.getDisplayName(); }

    public Date getTimestamp() { return timestamp; }
    public Map<String, AnalysisNode> getNodes() { return Collections.unmodifiableMap(nodes); }
    public List<CallRelation> getCallRelations() { return Collections.unmodifiableList(callRelations); }
    public Map<String, Integer> getStatistics() { return Collections.unmodifiableMap(statistics); }
    public List<String> getErrors() { return Collections.unmodifiableList(errors); }

    // 便捷方法
    public Collection<AnalysisNode> getNodesByType(AnalysisNode.NodeType type) {
        return nodes.values().stream()
            .filter(node -> node.getType() == type)
            .toList();
    }

    public List<CallRelation> getCallRelationsForCaller(String callerId) {
        return callRelations.stream()
            .filter(relation -> relation.getCaller().getId().equals(callerId))
            .toList();
    }

    public List<CallRelation> getCallRelationsForCallee(String calleeId) {
        return callRelations.stream()
            .filter(relation -> relation.getCallee().getId().equals(calleeId))
            .toList();
    }
}