package com.sankuai.deepcode.astplugin;

import com.intellij.lang.Language;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.sankuai.deepcode.astplugin.analyzer.JavaASTAnalyzer;
import com.sankuai.deepcode.astplugin.analyzer.PythonASTAnalyzer;
import com.sankuai.deepcode.astplugin.model.AnalysisNode;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.CallRelation;

import java.util.*;

/**
 * DeepCode AST分析器主入口
 *
 * 负责根据文件类型分发到相应的专用分析器
 *
 * <AUTHOR>
 */
public class DeepCodeASTAnalyzer {

    private final JavaASTAnalyzer javaAnalyzer = new JavaASTAnalyzer();
    private final PythonASTAnalyzer pythonAnalyzer = new PythonASTAnalyzer();

    // 缓存语言支持检测结果
    private Boolean javaSupported = null;
    private Boolean pythonSupported = null;

    /**
     * 分析指定的PSI文件
     *
     * @param psiFile 要分析的文件
     * @return 分析结果
     */
    public AnalysisResult analyze(PsiFile psiFile) {
        try {
            Language language = psiFile.getLanguage();
            String languageId = language.getID();

            // 根据文件类型和语言支持情况分流到不同的分析器
            if ("JAVA".equals(languageId) || psiFile.getName().endsWith(".java")) {
                // Java 支持：优先使用完整分析，降级到基础分析
                if (isJavaSupported() && isJavaFileInstance(psiFile)) {
                    return analyzeJavaFile(psiFile);
                } else {
                    // 降级到基础 Java 文件分析
                    return analyzeJavaFileBasic(psiFile);
                }
            } else if ("Python".equals(languageId) || psiFile.getName().endsWith(".py")) {
                if (isPythonSupported()) {
                    return pythonAnalyzer.analyze(psiFile);
                } else {
                    return createUnsupportedLanguageResult(psiFile, "Python", "Python language support not available. Install Python plugin for enhanced analysis.");
                }
            } else {
                return analyzeGenericFile(psiFile);
            }

        } catch (Exception e) {
            AnalysisResult errorResult = new AnalysisResult(psiFile.getName(),
                                                          psiFile.getLanguage().getDisplayName());
            errorResult.addError("Error during analysis: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 生成AST报告
     *
     * @param psiFile 要分析的文件
     * @return 格式化的报告字符串
     */
    public String generateASTReport(PsiFile psiFile) {
        try {
            AnalysisResult result = analyze(psiFile);
            return formatAnalysisResult(result);
        } catch (Exception e) {
            return "Error during analysis: " + e.getMessage();
        }
    }

    private AnalysisResult analyzeGenericFile(PsiFile psiFile) {
        AnalysisResult result = new AnalysisResult(psiFile.getName(),
                                                  psiFile.getLanguage().getDisplayName());

        // 基本文件信息分析
        result.updateStatistics("file_size", psiFile.getTextLength());

        // 统计PSI元素类型
        Collection<PsiElement> allElements =
            com.intellij.psi.util.PsiTreeUtil.findChildrenOfType(psiFile, PsiElement.class);
        result.updateStatistics("total_elements", allElements.size());

        return result;
    }

    private String formatAnalysisResult(AnalysisResult result) {
        StringBuilder report = new StringBuilder();

        // 基本信息
        appendHeader(result, report);

        // 节点信息
        appendNodesAnalysis(result, report);

        // 调用关系分析
        appendCallRelationsAnalysis(result, report);

        // 统计信息
        appendStatistics(result, report);

        // 错误信息
        appendErrors(result, report);

        return report.toString();
    }

    private void appendHeader(AnalysisResult result, StringBuilder report) {
        report.append("=== AST Analysis Report ===\n");
        report.append("File: ").append(result.getFileName()).append("\n");
        report.append("Language: ").append(result.getLanguage()).append("\n");
        report.append("Timestamp: ").append(result.getTimestamp()).append("\n");
        report.append("\n");
    }

    private void appendNodesAnalysis(AnalysisResult result, StringBuilder report) {
        Map<String, AnalysisNode> nodes = result.getNodes();
        if (nodes.isEmpty()) {
            return;
        }

        report.append("=== Code Structure ===\n");

        // 按类型分组显示节点
        Map<AnalysisNode.NodeType, List<AnalysisNode>> nodesByType = new HashMap<>();
        for (AnalysisNode node : nodes.values()) {
            nodesByType.computeIfAbsent(node.getType(), k -> new ArrayList<>()).add(node);
        }

        for (AnalysisNode.NodeType type : AnalysisNode.NodeType.values()) {
            List<AnalysisNode> typeNodes = nodesByType.get(type);
            if (typeNodes != null && !typeNodes.isEmpty()) {
                report.append(type.name()).append("S (").append(typeNodes.size()).append("):\n");
                typeNodes.stream()
                    .sorted(Comparator.comparing(AnalysisNode::getSignature))
                    .forEach(node -> report.append("  ").append(node.toString()).append("\n"));
                report.append("\n");
            }
        }
    }

    private void appendCallRelationsAnalysis(AnalysisResult result, StringBuilder report) {
        List<CallRelation> relations = result.getCallRelations();
        if (relations.isEmpty()) {
            return;
        }

        report.append("=== Call Relations Analysis ===\n");
        report.append("Total call relations: ").append(relations.size()).append("\n\n");

        // 按调用方分组聚合调用关系
        Map<String, List<CallRelation>> relationsByCaller = new LinkedHashMap<>();
        for (CallRelation relation : relations) {
            String callerSignature = relation.getCaller().getSignature();
            relationsByCaller.computeIfAbsent(callerSignature, k -> new ArrayList<>()).add(relation);
        }

        // 按调用方分组显示
        report.append("Call Relations (Grouped by Caller):\n");
        for (Map.Entry<String, List<CallRelation>> entry : relationsByCaller.entrySet()) {
            String callerSignature = entry.getKey();
            List<CallRelation> callerRelations = entry.getValue();

            // 显示调用方信息
            report.append("\n📞 CALLER: ").append(callerSignature).append("\n");
            report.append("   ID: ").append(callerRelations.get(0).getCaller().getId()).append("\n");
            report.append("   Calls ").append(callerRelations.size()).append(" different methods:\n");

            // 显示该调用方的所有调用关系
            for (int i = 0; i < callerRelations.size(); i++) {
                CallRelation relation = callerRelations.get(i);
                report.append("   ").append(i + 1).append(". → ");

                // 显示被调用方信息
                report.append(relation.getCallee().getSignature());
                if (relation.isExternal()) {
                    report.append(" [EXTERNAL]");
                }

                // 显示调用次数
                if (relation.getCallCount() > 1) {
                    report.append(" (").append(relation.getCallCount()).append(" calls)");
                }
                report.append("\n");

                // 显示被调用方完整ID
                report.append("      Callee ID: ").append(relation.getCallee().getId()).append("\n");

                // 显示调用实例详情
                if (!relation.getAllCallInstances().isEmpty()) {
                    report.append("      Call instances:\n");
                    for (CallRelation.CallInstance instance : relation.getAllCallInstances()) {
                        report.append("        - Line ").append(instance.getLineNumber())
                              .append(": ").append(instance.getExpression()).append("\n");
                    }
                }
            }
        }

        report.append("\n");

        // 调用统计
        appendCallStatistics(result, report);
    }

    private void appendCallStatistics(AnalysisResult result, StringBuilder report) {
        List<CallRelation> relations = result.getCallRelations();

        // 最活跃的调用者
        Map<String, Long> callerCounts = relations.stream()
            .collect(java.util.stream.Collectors.groupingBy(
                r -> r.getCaller().getSignature(),
                java.util.stream.Collectors.counting()
            ));

        if (!callerCounts.isEmpty()) {
            report.append("Most Active Callers:\n");
            callerCounts.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(5)
                .forEach(entry -> report.append("  ")
                    .append(entry.getKey())
                    .append(" → ")
                    .append(entry.getValue())
                    .append(" calls\n"));
            report.append("\n");
        }

        // 最常被调用的方法
        Map<String, Long> calleeCounts = relations.stream()
            .collect(java.util.stream.Collectors.groupingBy(
                r -> r.getCallee().getSignature(),
                java.util.stream.Collectors.counting()
            ));

        if (!calleeCounts.isEmpty()) {
            report.append("Most Called Methods:\n");
            calleeCounts.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(5)
                .forEach(entry -> report.append("  ")
                    .append(entry.getKey())
                    .append(" ← ")
                    .append(entry.getValue())
                    .append(" calls\n"));
            report.append("\n");
        }

        // 外部调用统计
        long externalCalls = relations.stream()
            .mapToLong(r -> r.isExternal() ? 1 : 0)
            .sum();

        if (externalCalls > 0) {
            report.append("External calls: ").append(externalCalls).append("\n\n");
        }
    }

    private void appendStatistics(AnalysisResult result, StringBuilder report) {
        Map<String, Integer> statistics = result.getStatistics();
        if (statistics.isEmpty()) {
            return;
        }

        report.append("=== Statistics ===\n");
        statistics.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> report.append(entry.getKey())
                .append(": ")
                .append(entry.getValue())
                .append("\n"));
        report.append("\n");
    }

    private void appendErrors(AnalysisResult result, StringBuilder report) {
        List<String> errors = result.getErrors();
        if (errors.isEmpty()) {
            return;
        }

        report.append("=== Errors ===\n");
        errors.forEach(error -> report.append("ERROR: ").append(error).append("\n"));
        report.append("\n");
    }

    /**
     * 检查Java语言支持是否可用
     */
    private boolean isJavaSupported() {
        if (javaSupported == null) {
            try {
                // 尝试加载Java相关的类来检测Java支持是否可用
                Class.forName("com.intellij.psi.PsiJavaFile");
                javaSupported = true;
            } catch (ClassNotFoundException e) {
                javaSupported = false;
            }
        }
        return javaSupported;
    }

    /**
     * 检查Python语言支持是否可用
     */
    private boolean isPythonSupported() {
        if (pythonSupported == null) {
            try {
                // 尝试加载Python相关的类来检测Python插件是否可用
                Class.forName("com.jetbrains.python.psi.PyFile");
                pythonSupported = true;
            } catch (ClassNotFoundException e) {
                pythonSupported = false;
            }
        }
        return pythonSupported;
    }

    /**
     * 使用反射分析 Java 文件
     */
    private AnalysisResult analyzeJavaFile(PsiFile psiFile) {
        try {
            // 检查文件是否为 PsiJavaFile 实例
            Class<?> psiJavaFileClass = Class.forName("com.intellij.psi.PsiJavaFile");
            if (!psiJavaFileClass.isInstance(psiFile)) {
                return createUnsupportedLanguageResult(psiFile, "Java", "File is not a valid Java file");
            }

            // 使用反射调用 Java 分析器
            java.lang.reflect.Method analyzeMethod = javaAnalyzer.getClass().getMethod("analyze", psiJavaFileClass);
            Object result = analyzeMethod.invoke(javaAnalyzer, psiFile);

            if (result instanceof AnalysisResult) {
                return (AnalysisResult) result;
            } else {
                return createUnsupportedLanguageResult(psiFile, "Java", "Unexpected analysis result type");
            }

        } catch (ClassNotFoundException e) {
            return createUnsupportedLanguageResult(psiFile, "Java", "Java plugin not available: " + e.getMessage());
        } catch (Exception e) {
            return createUnsupportedLanguageResult(psiFile, "Java", "Error analyzing Java file: " + e.getMessage());
        }
    }

    /**
     * 检查是否为 PsiJavaFile 实例
     */
    private boolean isJavaFileInstance(PsiFile psiFile) {
        try {
            Class<?> psiJavaFileClass = Class.forName("com.intellij.psi.PsiJavaFile");
            return psiJavaFileClass.isInstance(psiFile);
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    /**
     * 基础 Java 文件分析（降级方案）
     */
    private AnalysisResult analyzeJavaFileBasic(PsiFile psiFile) {
        AnalysisResult result = new AnalysisResult(psiFile.getName(), "Java");

        // 基础文件信息
        result.updateStatistics("file_size", psiFile.getTextLength());
        result.updateStatistics("language_supported", 0);

        // 简单的文本分析
        String content = psiFile.getText();
        if (content != null) {
            // 统计基本信息
            int classCount = countOccurrences(content, "class ");
            int methodCount = countOccurrences(content, "public ") + countOccurrences(content, "private ");
            int importCount = countOccurrences(content, "import ");

            result.updateStatistics("estimated_classes", classCount);
            result.updateStatistics("estimated_methods", methodCount);
            result.updateStatistics("estimated_imports", importCount);
        }

        result.addError("Java plugin not fully available. Using basic text analysis. Install/enable Java plugin for enhanced analysis.");
        return result;
    }

    /**
     * 统计字符串出现次数
     */
    private int countOccurrences(String text, String pattern) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(pattern, index)) != -1) {
            count++;
            index += pattern.length();
        }
        return count;
    }

    /**
     * 创建不支持语言的结果
     */
    private AnalysisResult createUnsupportedLanguageResult(PsiFile psiFile, String languageName, String message) {
        AnalysisResult result = new AnalysisResult(psiFile.getName(), languageName);
        result.addError(message);

        // 添加基本的文件信息
        result.updateStatistics("file_size", psiFile.getTextLength());
        result.updateStatistics("language_supported", 0);

        return result;
    }
}
