plugins {
    id("java")
    id("org.jetbrains.intellij") version "1.17.4"
}

group = "com.deepcode"
version = "1.0-SNAPSHOT"

// 明确指定Java版本
java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

intellij {
    version.set("2024.1.4")
    type.set("IC") // 使用IntelliJ Community版本

    // 混合依赖策略：Java插件强制依赖（广泛支持），Python插件动态检测
    plugins.set(listOf(
        "com.intellij.java"
    ))

    // 配置沙箱目录，确保插件和配置持久化
    sandboxDir.set("${project.buildDir}/idea-sandbox")

    // 下载插件源码，便于调试
    downloadSources.set(true)

    // 确保插件兼容性
    pluginName.set("AST Analysis Plugin")
}

tasks {
    buildSearchableOptions {
        enabled = false
    }

    patchPluginXml {
        sinceBuild.set("241")
        untilBuild.set("242.*")
    }

    runIde {
        // 启用自动重载插件
        autoReloadPlugins.set(true)
        // 让Gradle自动下载和配置IDE
        maxHeapSize = "2g"

        // 优化JVM参数，避免版本解析问题
        jvmArgs = listOf(
            "-Xms512m",
            "-Xmx2g",
            "-XX:ReservedCodeCacheSize=512m",
            "-XX:+UseConcMarkSweepGC",
            "-XX:SoftRefLRUPolicyMSPerMB=50",
            "-ea",
            "-XX:CICompilerCount=2",
            "-Dsun.io.useCanonPrefixCache=false",
            "-Djdk.http.auth.tunneling.disabledSchemes=\"\"",
            "-XX:+HeapDumpOnOutOfMemoryError",
            "-XX:-OmitStackTraceInFastThrow",
            // 添加系统属性来避免Gradle版本解析问题
            "-Dgradle.user.home=${System.getProperty("user.home")}/.gradle",
            "-Djava.version=17",
            // 预配置插件设置
            "-Didea.plugins.path=${project.buildDir}/idea-sandbox/plugins",
            "-Didea.config.path=${project.buildDir}/idea-sandbox/config",
            "-Didea.system.path=${project.buildDir}/idea-sandbox/system"
        )

        // 配置系统属性
        systemProperties = mapOf(
            "idea.auto.reload.plugins" to "true",
            "idea.plugin.in.sandbox.mode" to "true"
        )
    }

    // 确保编译时使用正确的Java版本
    compileJava {
        options.release.set(17)
    }

    test {
        useJUnitPlatform()
        testLogging {
            events("PASSED", "SKIPPED", "FAILED")
        }
    }

    // 创建自定义任务来设置沙箱环境
    register("setupSandbox") {
        group = "intellij"
        description = "Setup sandbox environment with required plugins"

        doLast {
            val sandboxDir = file("${project.layout.buildDirectory.get()}/idea-sandbox")
            val configDir = file("$sandboxDir/config")
            val pluginsDir = file("$sandboxDir/plugins")
            val optionsDir = file("$configDir/options")

            // 创建必要的目录
            configDir.mkdirs()
            pluginsDir.mkdirs()
            optionsDir.mkdirs()

            // 创建插件管理器配置，启用Python插件自动安装
            val pluginManagerFile = file("$optionsDir/pluginAdvertiser.xml")
            pluginManagerFile.writeText("""
                <application>
                  <component name="PluginAdvertiserSettings">
                    <option name="suggestionIntervalDays" value="0" />
                    <option name="enabledForProject" value="true" />
                  </component>
                </application>
            """.trimIndent())

            // 创建IDE配置，预配置Python支持
            val ideaConfigFile = file("$configDir/idea.properties")
            ideaConfigFile.writeText("""
                # IDE配置
                idea.auto.reload.plugins=true
                idea.plugin.suggest.python=true
                idea.plugin.auto.install.python=true
            """.trimIndent())

            println("Sandbox environment setup completed at: $sandboxDir")
            println("Python plugin will be suggested for installation on first startup")
        }
    }

    // 创建运行设置脚本的任务
    register("setupPythonPlugin", Exec::class) {
        group = "intellij"
        description = "Setup Python plugin for sandbox IDE"

        // 根据操作系统选择脚本
        if (System.getProperty("os.name").toLowerCase().contains("windows")) {
            commandLine("cmd", "/c", "scripts\\setup-python-plugin.bat")
        } else {
            commandLine("bash", "scripts/setup-python-plugin.sh")
        }

        // 确保脚本存在
        doFirst {
            val scriptFile = if (System.getProperty("os.name").toLowerCase().contains("windows")) {
                file("scripts/setup-python-plugin.bat")
            } else {
                file("scripts/setup-python-plugin.sh")
            }

            if (!scriptFile.exists()) {
                throw GradleException("Setup script not found: ${scriptFile.absolutePath}")
            }
        }
    }

    // 让runIde依赖setupSandbox和setupPythonPlugin
    runIde {
        dependsOn("setupSandbox", "setupPythonPlugin")
    }
}

configurations.all {
    exclude(group = "org.jetbrains", module = "annotations")
    exclude(group = "org.junit.jupiter")
}

dependencies {
    // 测试依赖已排除，因为在IntelliJ插件环境中不兼容
}