# Python节点ID生成规则

## 概述

修复后的Python AST分析器严格遵循指定的路径格式来生成节点ID，确保Python文件中模块、类、方法等节点的ID唯一性。

## ID生成规则

### 1. 模块ID
- **格式**: `module_name`
- **示例**: 
  - 文件 `test_module.py` → 模块ID: `test_module`
  - 文件 `utils.py` → 模块ID: `utils`

### 2. 类ID
- **格式**: `module.class_name`
- **示例**:
  - 模块 `test_module` 中的类 `Animal` → 类ID: `test_module.Animal`
  - 模块 `utils` 中的类 `Helper` → 类ID: `utils.Helper`

### 3. 函数ID

#### 3.1 模块级函数
- **格式**: `module.function_name`
- **示例**:
  - 模块 `test_module` 中的函数 `create_pets` → 函数ID: `test_module.create_pets`
  - 模块 `utils` 中的函数 `helper_func` → 函数ID: `utils.helper_func`

#### 3.2 类中方法
- **格式**: `module.class_name.method_name`
- **示例**:
  - 模块 `test_module` 中类 `Animal` 的方法 `__init__` → 方法ID: `test_module.Animal.__init__`
  - 模块 `test_module` 中类 `Dog` 的方法 `speak` → 方法ID: `test_module.Dog.speak`

### 4. 变量ID
- **格式**: `module.variable_name`
- **示例**:
  - 模块 `test_module` 中的变量 `global_var` → 变量ID: `test_module.global_var`

### 5. 外部节点ID
- **格式**: `external.type.context.node_name`
- **示例**:
  - super()调用 → `external.function.super.ClassName.__init__`
  - 父类方法调用 → `external.function.ParentClass.method_name`

### 6. 内置节点ID
- **格式**: `builtin.type.node_name`
- **示例**:
  - 内置函数 → `builtin.function.print`
  - 内置类型 → `builtin.type.str`

## 实际示例

假设有以下Python文件 `test_inheritance.py`:

```python
class Animal:
    def __init__(self, name):
        self.name = name
    
    def speak(self):
        print(f"{self.name} makes a sound")

class Dog(Animal):
    def __init__(self, name, breed):
        super().__init__(name)  # super()调用
        self.breed = breed
    
    def speak(self):
        super().speak()  # super()方法调用
        print(f"{self.name} barks")

def create_dog():
    return Dog("Buddy", "Golden")

global_var = "test"
```

### 生成的节点ID:

#### 类节点
- `test_inheritance.Animal`
- `test_inheritance.Dog`

#### 方法节点
- `test_inheritance.Animal.__init__`
- `test_inheritance.Animal.speak`
- `test_inheritance.Dog.__init__`
- `test_inheritance.Dog.speak`

#### 函数节点
- `test_inheritance.create_dog`

#### 变量节点
- `test_inheritance.global_var`

#### 外部节点（super()调用）
- `external.function.super.Dog.__init__`
- `external.function.super().speak`

## 唯一性保证

### 1. 模块级唯一性
- 每个模块内的类名、函数名、变量名必须唯一
- 通过模块前缀确保跨模块的唯一性

### 2. 类级唯一性
- 同一类中的方法名通过类前缀区分
- 不同类中的同名方法通过完整路径区分

### 3. 外部调用唯一性
- super()调用和父类调用使用特殊前缀
- 通过上下文信息确保唯一性

## 优势

### 1. 路径清晰
- 每个ID都反映了节点在代码结构中的完整路径
- 便于理解和调试

### 2. 唯一性保证
- 严格的命名规则确保不会有ID冲突
- 支持复杂的继承和组合关系

### 3. 扩展性好
- 规则简单明确，易于扩展
- 支持多层嵌套和复杂结构

### 4. 工具友好
- 标准化的ID格式便于工具处理
- 支持自动化分析和重构

## 注意事项

### 1. 模块名处理
- 如果无法确定模块名，使用 `unknown_module` 作为默认值
- 确保即使在异常情况下也能生成有效ID

### 2. 特殊字符处理
- Python标识符中的特殊字符（如下划线）保持原样
- 确保生成的ID符合标识符规范

### 3. 大小写敏感
- 严格按照Python源码中的大小写生成ID
- 区分不同大小写的标识符

这套ID生成规则确保了Python AST分析器能够为每个代码元素生成唯一、有意义的标识符，为后续的代码分析和处理提供了可靠的基础。