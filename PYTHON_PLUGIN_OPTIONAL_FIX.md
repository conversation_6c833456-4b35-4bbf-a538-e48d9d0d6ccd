# Python 插件可选依赖修复方案

## 🎯 问题解决

**原问题**：插件强制要求安装Python插件，导致错误：
```
Plugin 'AST Analysis Plugin' requires plugin 'com.intellij.modules.python' to be installed
```

**解决方案**：将Python插件设为可选依赖，支持有/无Python插件两种模式

## 🔧 技术实现

### 1. 插件依赖配置修改

**plugin.xml 配置**：
```xml
<depends>com.intellij.modules.platform</depends>
<depends>com.intellij.java</depends>
<depends optional="true" config-file="python-support.xml">com.intellij.modules.python</depends>
```

**关键改动**：
- 添加 `optional="true"` 属性
- 使用独立的 `python-support.xml` 配置文件

### 2. Python插件可用性检测

**动态检测机制**：
```java
private boolean isPythonPluginAvailable() {
    try {
        // 尝试加载Python相关的类来检测插件是否可用
        Class.forName("com.jetbrains.python.psi.PyFile");
        debugLog("Python plugin is available");
        return true;
    } catch (ClassNotFoundException e) {
        debugLog("Python plugin not available: " + e.getMessage());
        return false;
    }
}
```

### 3. 双模式分析支持

#### 模式1：增强分析（Python插件可用）
- 使用完整的Python PSI API
- 支持递归调用检测
- 支持内置方法调用分析
- 支持复杂的Python语法结构

#### 模式2：基础分析（无Python插件）
- 使用文本模式分析
- 基础的类、函数、导入识别
- 提供友好的错误提示
- 保持基本功能可用

## 📊 功能对比

| 功能特性 | 有Python插件 | 无Python插件 |
|---------|-------------|-------------|
| 基础结构识别 | ✅ 完整支持 | ✅ 基础支持 |
| 类定义分析 | ✅ 完整PSI分析 | ✅ 正则表达式分析 |
| 函数定义分析 | ✅ 完整PSI分析 | ✅ 正则表达式分析 |
| 导入语句分析 | ✅ 完整PSI分析 | ✅ 正则表达式分析 |
| 递归调用检测 | ✅ 支持 | ❌ 不支持 |
| 内置方法调用 | ✅ 支持 | ❌ 不支持 |
| 复杂语法支持 | ✅ 支持 | ⚠️ 有限支持 |

## 🚀 使用体验

### 有Python插件时
```
Starting Python analysis for file: test_python_sample.py
Python plugin is available
Detected Python module path: test_python_sample
Python analysis completed successfully. Found 15 nodes and 8 call relations
```

### 无Python插件时
```
Starting Python analysis for file: test_python_sample.py
Python plugin not available
Using basic Python analysis for: test_python_sample
Python plugin not installed. Install 'Python Community Edition' plugin for enhanced analysis.
Basic Python analysis completed. Found 8 nodes
```

## 🛠️ 实现细节

### 1. 分析流程控制
```java
public AnalysisResult analyze(PsiFile pythonFile) {
    return ReadAction.compute(() -> {
        AnalysisResult result = new AnalysisResult(pythonFile.getName(), "Python");
        
        try {
            // 检查Python插件是否可用
            if (!isPythonPluginAvailable()) {
                debugLog("Python plugin not available, using basic analysis");
                result.addError("Python plugin not installed. Install 'Python Community Edition' plugin for enhanced analysis.");
                return analyzeBasicPythonStructure(pythonFile, result);
            }
            
            // 使用完整的Python PSI分析
            // ...
        } catch (Exception e) {
            // 错误处理
        }
        
        return result;
    });
}
```

### 2. 基础分析实现
```java
private void analyzeBasicPythonElements(PsiFile pythonFile, AnalysisResult result, String moduleName, String filePath) {
    String fileText = pythonFile.getText();
    
    // 基础类分析
    analyzeBasicClasses(fileText, result, moduleName, filePath);
    
    // 基础函数分析
    analyzeBasicFunctions(fileText, result, moduleName, filePath);
    
    // 基础导入分析
    analyzeBasicImports(fileText, result);
}
```

### 3. 用户友好的错误提示
- 明确说明Python插件未安装
- 提供安装指导
- 说明功能限制
- 保持基础功能可用

## 📋 测试验证

### 测试场景1：无Python插件环境
```bash
# 在纯IntelliJ IDEA Community Edition中测试
./gradlew runIde
# 打开Python文件，使用AST分析功能
# 预期：显示基础分析结果 + 友好提示
```

### 测试场景2：有Python插件环境
```bash
# 安装Python Community Edition插件后测试
# 预期：显示完整分析结果，包括递归调用和内置方法
```

## 🎯 优势总结

### 1. **兼容性**
- ✅ 支持纯IntelliJ IDEA环境
- ✅ 支持PyCharm环境
- ✅ 支持WebStorm + Python插件环境

### 2. **用户体验**
- ✅ 不强制安装Python插件
- ✅ 提供清晰的功能说明
- ✅ 渐进式功能增强

### 3. **开发友好**
- ✅ 代码结构清晰
- ✅ 错误处理完善
- ✅ 调试信息详细

### 4. **功能完整性**
- ✅ 基础功能始终可用
- ✅ 高级功能按需启用
- ✅ 平滑的功能降级

## 🔄 升级路径

用户可以随时安装Python插件来获得增强功能：

1. **当前状态**：基础Python分析
2. **安装Python插件**：`File` → `Settings` → `Plugins` → 搜索 "Python Community Edition"
3. **重启IDE**：自动启用增强分析功能
4. **享受完整功能**：递归检测、内置方法调用等

这个解决方案确保了插件的通用性和用户友好性，同时保持了代码的可维护性和扩展性。
