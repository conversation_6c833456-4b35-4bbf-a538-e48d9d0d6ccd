#!/bin/bash

# AST Analysis Plugin - Build Verification Script
# This script verifies that the plugin can be built successfully

set -e  # Exit on any error

echo "🔍 AST Analysis Plugin - Build Verification"
echo "=========================================="

# Check Java version
echo "📋 Checking Java version..."
java -version
echo ""

# Check if we're in the right directory
if [ ! -f "build.gradle.kts" ]; then
    echo "❌ Error: build.gradle.kts not found. Please run this script from the project root."
    exit 1
fi

echo "🧹 Cleaning previous builds..."
./gradlew clean

echo ""
echo "🔨 Compiling Java sources..."
./gradlew compileJava

echo ""
echo "📦 Building plugin..."
./gradlew buildPlugin

echo ""
echo "✅ Build verification completed successfully!"
echo ""

# Check if the plugin ZIP was created
if [ -f "build/distributions/AST Analysis Plugin-1.0-SNAPSHOT.zip" ]; then
    echo "📁 Plugin package created:"
    ls -lh "build/distributions/AST Analysis Plugin-1.0-SNAPSHOT.zip"
    echo ""
    echo "🎉 The plugin is ready for installation!"
    echo ""
    echo "To install:"
    echo "1. Open IntelliJ IDEA/PyCharm/WebStorm"
    echo "2. Go to File → Settings → Plugins"
    echo "3. Click gear icon → Install Plugin from Disk..."
    echo "4. Select: build/distributions/AST Analysis Plugin-1.0-SNAPSHOT.zip"
    echo "5. Restart the IDE"
    echo ""
    echo "To test in development mode:"
    echo "./gradlew runIde"
else
    echo "❌ Error: Plugin package not found!"
    exit 1
fi
